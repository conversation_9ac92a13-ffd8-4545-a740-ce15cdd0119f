export interface Job {
  jobId?: string | null;
  jobTitle: string;
  company: string;
  description: string;
  jobType: string;
  location: string;
  mode: string;
  salaryPackage: number | null;
  requirements: string | string[];
  // Additional properties used in template
  title?: string;
  type?: string;
  department?: string;
  featured?: boolean;
  endDate?: Date | string;
  posted?: Date | string;
}


