import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardModule } from 'primeng/card';
import { DialogModule } from 'primeng/dialog';
import { FormsModule } from '@angular/forms';
import { FileUploadModule } from 'primeng/fileupload';
import { JobService } from '../../services/job.service';
import { AppliedJobService } from '../../services/applied-job.service';
import { Job } from '../../models/job.model';

@Component({
  selector: 'app-careers',
  standalone: true,
  imports: [CommonModule, CardModule, DialogModule, FormsModule,FileUploadModule],
  templateUrl: './careers.component.html',
  styleUrls: ['./careers.component.css']
})
export class CareersComponent implements OnInit {
  jobs: Job[] = [];

  selectedJob: Job | null = null;
  displayDialog: boolean = false;
  application: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    address: string;
    coverLetter: string;
    resume?: File;
  } = {
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    coverLetter: "",
  };
  files: { [key: string]: File } = {};
  appService: any;

  // Filters
  selectedLocation: string = "All Locations";
  selectedType: string = "All Types";
  selectedDepartment: string = "All Departments";
  searchTerm: string = "";

  locationOptions: string[] = ["All Locations", "Remote", "San Francisco, CA", "New York, NY"];
  typeOptions: string[] = ["All Types", "Full-time", "Part-time", "Contract"];
  departmentOptions: string[] = ["All Departments", "Engineering", "Design", "Marketing", "AI/ML"];

   constructor(private jobService: JobService,
    private appliedJob : AppliedJobService
   ) {}
  

  ngOnInit(): void {
    this.loadJobs();
  }

  isDragActive: boolean = false;
  resumeFile: File | null = null;

onDragOver(event: DragEvent): void {
  event.preventDefault();
  this.isDragActive = true;
}

onDragLeave(event: DragEvent): void {
  event.preventDefault();
  this.isDragActive = false;
}

onFileDrop(event: DragEvent, type: string): void {
  event.preventDefault();
  this.isDragActive = false;

  if (event.dataTransfer?.files.length) {
    const file = event.dataTransfer.files[0];
    this.setFile(file, type);
  }
}

onFileChange(event: Event, type: string): void {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files[0]) {
    const file = input.files[0];
    this.setFile(file, type);
  }
}

setFile(file: File, type: string): void {
  debugger;
  if (type === 'resume') {
    this.resumeFile = file;
    this.application.resume = file;
  }
}

  loadJobs(): void {
  this.jobService.getJobs().subscribe({
    next: (data: any[]) => {
      this.jobs = data.map((job: any) => ({
        ...job,
        requirements: job.requirements ? job.requirements.split(',').map((r: string) => r.trim()) : [],
        // Map common properties for template compatibility
        title: job.title || job.jobTitle,
        type: job.type || job.jobType
      }));
      // console.log('Jobs loaded:', this.jobs);
    },
    error: (err) => console.error('Error loading jobs', err),
    complete: () => console.log('Job loading complete')
  });
}

  openApplyDialog(job: Job) {
    this.selectedJob = job
    this.displayDialog = true
    this.application = {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      address: "",
      coverLetter: "",
    }
  }

  closeDialog(): void {
    this.displayDialog = false;
    this.selectedJob = null;
  }

//   onFileChange(event: any, type: string) {
//   const file = event.target.files?.[0]
//   if (file) {
//     this.files[type] = file

//     console.log("File",file, this.files)
//   } else {
//     delete this.files[type]
//   }
// }


submitApplication(): void {
  if (!this.application.firstName || !this.application.lastName || !this.application.email) {
    alert("Please fill in all required fields.");
    return;
  }

  if (!this.resumeFile) {
    alert("Please upload your resume.");
    return;
  }

  if (!this.selectedJob) {
    alert("No job selected.");
    return;
  }

  const formData = new FormData();
  formData.append("JobId", this.selectedJob.jobId?.toString() || '');
  formData.append("Email", this.application.email);
  formData.append("PhoneNumber", this.application.phone || '');
  formData.append("CoverLetterPath", this.application.coverLetter || '');
  formData.append("EmployerName", this.application.firstName + ' ' + this.application.lastName || '');
  formData.append("ResumeFile", this.resumeFile);

  this.appliedJob.createAppliedJob(formData).subscribe({
    next: () => {
      alert('Application submitted successfully!');
      this.closeDialog();
    },
    error: (err) => {
      console.error('Application submission failed', err.message);
      const errorMsg = err?.error?.message || err.message || 'Unknown error';
      alert(errorMsg);
      this.closeDialog();
    }
  });
}
  
  // Filter methods (optional - for future enhancement)
  getFilteredJobs(): Job[] {
    return this.jobs.filter((job) => {
      const locationMatch = this.selectedLocation === "All Locations" || job.location === this.selectedLocation
      const typeMatch = this.selectedType === "All Types" || (job.type || job.jobType) === this.selectedType
      const departmentMatch =
        this.selectedDepartment === "All Departments" || job.department === this.selectedDepartment
      const searchMatch =
        !this.searchTerm ||
        (job.title || job.jobTitle)?.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        job.company.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        job.description.toLowerCase().includes(this.searchTerm.toLowerCase())

      return locationMatch && typeMatch && departmentMatch && searchMatch
    })
  }

  // Utility methods
  getDaysRemaining(endDate: Date): number {
    const today = new Date()
    const end = new Date(endDate)
    const diffTime = end.getTime() - today.getTime()
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  isJobExpiringSoon(endDate: Date): boolean {
    return this.getDaysRemaining(endDate) <= 7
  }

  getJobTypeIcon(type: string): string {
    switch (type) {
      case "Full-time":
        return "💼"
      case "Part-time":
        return "⏰"
      case "Contract":
        return "📋"
      default:
        return "💼"
    }
  }

  clearFilters() {
    this.selectedLocation = "All Locations"
    this.selectedType = "All Types"
    this.selectedDepartment = "All Departments"
    this.searchTerm = ""
  }
}
